import asyncio
import re
import json
import requests
from playwright.async_api import async_playwright
from urllib.parse import urljoin
from bs4 import BeautifulSoup

class TuneCasterSitemapCollector:
    def __init__(self):
        self.base_url = "https://tunecaster.com"
        self.pop_urls = []
        self.rock_urls = []
        self.all_urls = []
    
    async def discover_from_sitemap(self):
        print("Discovering chart URLs from TuneCaster sitemap...")
        
        # Try different sitemap URLs
        sitemap_urls = [
            'https://tunecaster.com/sitemap.xml',
            'https://tunecaster.com/sitemap.txt',
            'https://tunecaster.com/robots.txt'
        ]
        
        # First try to get sitemap
        sitemap_found = False
        for sitemap_url in sitemap_urls:
            try:
                print(f"Trying sitemap: {sitemap_url}")
                response = requests.get(sitemap_url, timeout=30)
                if response.status_code == 200:
                    print(f"✓ Found sitemap at: {sitemap_url}")
                    await self.parse_sitemap_content(response.text, sitemap_url)
                    sitemap_found = True
                    break
            except Exception as e:
                print(f"✗ Failed to access {sitemap_url}: {e}")
        
        # If no sitemap found, crawl the website
        if not sitemap_found:
            print("No sitemap found. Crawling website for chart URLs...")
            await self.crawl_website_for_charts()
        
        # Process and categorize URLs
        self.categorize_urls()
        self.show_summary()
        self.save_to_json()
    
    async def parse_sitemap_content(self, content, sitemap_url):
        """Parse sitemap content to extract chart URLs"""
        try:
            if sitemap_url.endswith('.xml'):
                # Parse XML sitemap
                soup = BeautifulSoup(content, 'xml')
                urls = soup.find_all('url')
                for url_tag in urls:
                    loc = url_tag.find('loc')
                    if loc:
                        url = loc.text.strip()
                        if self.is_chart_url(url):
                            self.all_urls.append(url)
            
            elif sitemap_url.endswith('.txt'):
                # Parse text sitemap
                lines = content.split('\n')
                for line in lines:
                    url = line.strip()
                    if url and self.is_chart_url(url):
                        self.all_urls.append(url)
            
            elif 'robots.txt' in sitemap_url:
                # Look for sitemap references in robots.txt
                lines = content.split('\n')
                for line in lines:
                    if line.lower().startswith('sitemap:'):
                        sitemap_ref = line.split(':', 1)[1].strip()
                        print(f"Found sitemap reference: {sitemap_ref}")
                        try:
                            response = requests.get(sitemap_ref, timeout=30)
                            if response.status_code == 200:
                                await self.parse_sitemap_content(response.text, sitemap_ref)
                        except Exception as e:
                            print(f"Failed to fetch referenced sitemap: {e}")
            
            print(f"Extracted {len(self.all_urls)} chart URLs from sitemap")
            
        except Exception as e:
            print(f"Error parsing sitemap content: {e}")
    
    async def crawl_website_for_charts(self):
        """Crawl the website to find chart URLs"""
        print("Crawling website for chart URLs...")
        
        # Start with main pages and discover chart links
        start_pages = [
            'https://tunecaster.com/',
            'https://tunecaster.com/charts/',
            'https://tunecaster.com/rock/',
            'https://tunecaster.com/pop/'
        ]
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                visited_pages = set()
                pages_to_visit = start_pages.copy()
                
                while pages_to_visit and len(visited_pages) < 50:  # Limit crawling
                    current_url = pages_to_visit.pop(0)
                    
                    if current_url in visited_pages:
                        continue
                    
                    visited_pages.add(current_url)
                    print(f"Crawling: {current_url}")
                    
                    try:
                        await page.goto(current_url, timeout=30000)
                        await page.wait_for_timeout(2000)
                        
                        # Get all links
                        links = await page.evaluate('''
                            () => {
                                const links = Array.from(document.querySelectorAll('a[href]'));
                                return links.map(link => link.href);
                            }
                        ''')
                        
                        for link in links:
                            if link.startswith('/'):
                                link = urljoin(self.base_url, link)
                            
                            # If it's a chart URL, add it
                            if self.is_chart_url(link):
                                self.all_urls.append(link)
                            
                            # If it's a potential chart index page, add to crawl list
                            elif self.is_chart_index_page(link) and link not in visited_pages:
                                pages_to_visit.append(link)
                        
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        print(f"Error crawling {current_url}: {e}")
                
            finally:
                await browser.close()
        
        print(f"Crawling completed. Found {len(self.all_urls)} chart URLs")
    
    def is_chart_url(self, url):
        """Check if URL is a chart URL"""
        # Pop charts: /charts/XX/weekXXXX.html
        # Rock charts: /charts/XX/rockXXXX.html
        return bool(re.search(r'/charts/[0-9]+/(week|rock)[0-9]+\.html', url))
    
    def is_chart_index_page(self, url):
        """Check if URL might contain chart links"""
        chart_indicators = [
            '/chart', '/rock', '/pop', '/charts/',
            'chart0.html', 'chart1.html', 'chart6.html', 
            'chart7.html', 'chart8.html', 'chart9.html',
            'rock0.html', 'rock8.html', 'rock9.html'
        ]
        return any(indicator in url.lower() for indicator in chart_indicators)
    
    def categorize_urls(self):
        """Categorize URLs into pop and rock"""
        # Remove duplicates
        self.all_urls = list(set(self.all_urls))
        
        for url in self.all_urls:
            if re.search(r'/charts/[0-9]+/week[0-9]+\.html', url):
                self.pop_urls.append(url)
            elif re.search(r'/charts/[0-9]+/rock[0-9]+\.html', url):
                self.rock_urls.append(url)
        
        # Sort URLs
        self.pop_urls = sorted(self.pop_urls)
        self.rock_urls = sorted(self.rock_urls)
    
    def show_summary(self):
        """Show collection summary"""
        print("\n" + "="*60)
        print("TUNECASTER CHART URLS COLLECTION SUMMARY")
        print("="*60)
        
        print(f"POP CHART URLs: {len(self.pop_urls)}")
        print(f"ROCK CHART URLs: {len(self.rock_urls)}")
        print(f"TOTAL CHART URLs: {len(self.pop_urls) + len(self.rock_urls)}")
        
        # Show decade breakdown
        if self.pop_urls:
            print(f"\nPOP URLs by decade:")
            pop_decades = self.analyze_by_decade(self.pop_urls)
            for decade, count in sorted(pop_decades.items()):
                decade_name = self.get_decade_name(decade)
                print(f"  {decade_name}: {count} URLs")
        
        if self.rock_urls:
            print(f"\nROCK URLs by decade:")
            rock_decades = self.analyze_by_decade(self.rock_urls)
            for decade, count in sorted(rock_decades.items()):
                decade_name = self.get_decade_name(decade)
                print(f"  {decade_name}: {count} URLs")
        
        # Show sample URLs
        print(f"\nSample Pop URLs (first 3):")
        for i, url in enumerate(self.pop_urls[:3], 1):
            print(f"  {i}. {url}")
        
        print(f"\nSample Rock URLs (first 3):")
        for i, url in enumerate(self.rock_urls[:3], 1):
            print(f"  {i}. {url}")
        
        print("="*60)
    
    def analyze_by_decade(self, urls):
        """Analyze URLs by decade"""
        decade_count = {}
        for url in urls:
            match = re.search(r'/charts/(\d{2})/', url)
            if match:
                decade = match.group(1)
                decade_count[decade] = decade_count.get(decade, 0) + 1
        return decade_count
    
    def get_decade_name(self, decade_code):
        """Convert decade code to name"""
        decade_map = {
            '00': '2000s',
            '10': '2010s',
            '60': '1960s',
            '70': '1970s',
            '80': '1980s',
            '90': '1990s'
        }
        return decade_map.get(decade_code, f"Decade {decade_code}")
    
    def save_to_json(self):
        """Save URLs to JSON file"""
        try:
            data = {
                'collection_summary': {
                    'total_urls': len(self.pop_urls) + len(self.rock_urls),
                    'pop_urls_count': len(self.pop_urls),
                    'rock_urls_count': len(self.rock_urls),
                    'collection_method': 'sitemap_and_crawling',
                    'timestamp': str(asyncio.get_event_loop().time())
                },
                'pop_urls': self.pop_urls,
                'rock_urls': self.rock_urls,
                'all_chart_urls': self.pop_urls + self.rock_urls
            }
            
            with open('urls.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"\n✓ URLs saved to: urls.json")
            print(f"✓ Total URLs in file: {len(self.pop_urls) + len(self.rock_urls)}")
            
        except Exception as e:
            print(f"✗ Error saving to JSON: {e}")

async def main():
    collector = TuneCasterSitemapCollector()
    
    print("TuneCaster Sitemap URL Collector")
    print("Collecting all chart URLs from sitemap and website crawling")
    print("="*60)
    
    try:
        await collector.discover_from_sitemap()
        print("\n✓ URL collection completed successfully!")
        
    except KeyboardInterrupt:
        print("\n✗ Collection interrupted by user")
    except Exception as e:
        print(f"\n✗ Error during collection: {e}")

if __name__ == "__main__":
    asyncio.run(main())