import asyncio
import re
import json
import requests
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse
from datetime import datetime

class ChartURLFinder:
    def __init__(self):
        self.base_url = "https://tunecaster.com"
        self.pop_urls = set()
        self.rock_urls = set()
        self.visited_pages = set()
    
    async def find_all_chart_urls(self):
        print("Finding all chart URLs from TuneCaster website...")
        
        # Try sitemap first
        await self.try_sitemap()
        
        # If not enough URLs found, crawl the website
        total_found = len(self.pop_urls) + len(self.rock_urls)
        if total_found < 100:  # If we found very few URLs, crawl more
            print(f"Found only {total_found} URLs from sitemap. Starting website crawl...")
            await self.crawl_website()
        
        self.show_results()
        self.save_to_json()
    
    async def try_sitemap(self):
        """Try to get URLs from sitemap"""
        sitemap_urls = [
            'https://tunecaster.com/sitemap.xml',
            'https://tunecaster.com/sitemap.txt'
        ]
        
        for sitemap_url in sitemap_urls:
            try:
                print(f"Checking sitemap: {sitemap_url}")
                response = requests.get(sitemap_url, timeout=10)
                if response.status_code == 200:
                    self.parse_sitemap(response.text)
                    print(f"✓ Processed sitemap: {sitemap_url}")
                    return
            except:
                continue
        
        print("No accessible sitemap found. Will crawl website.")
    
    def parse_sitemap(self, content):
        """Parse sitemap content"""
        # Look for chart URLs in the content
        chart_pattern = r'https?://[^/]+/charts/\d+/(week|rock)\d+\.html'
        urls = re.findall(chart_pattern, content)
        
        # Also look for full URLs
        url_pattern = r'https?://[^\s<>"]+'
        all_urls = re.findall(url_pattern, content)
        
        for url in all_urls:
            if self.is_chart_url(url):
                self.categorize_url(url)
    
    async def crawl_website(self):
        """Crawl website to find chart URLs"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # Start with main page and known chart pages
                start_urls = [
                    'https://tunecaster.com/',
                    'https://tunecaster.com/charts/',
                ]
                
                # Add potential chart index pages
                for i in range(10):
                    start_urls.append(f'https://tunecaster.com/chart{i}.html')
                    start_urls.append(f'https://tunecaster.com/rock{i}.html')
                
                urls_to_visit = start_urls.copy()
                
                while urls_to_visit and len(self.visited_pages) < 100:
                    current_url = urls_to_visit.pop(0)
                    
                    if current_url in self.visited_pages:
                        continue
                    
                    self.visited_pages.add(current_url)
                    print(f"Crawling: {current_url}")
                    
                    try:
                        await page.goto(current_url, timeout=15000)
                        await page.wait_for_timeout(1000)
                        
                        # Get all links
                        links = await page.evaluate('''
                            () => {
                                const links = Array.from(document.querySelectorAll('a[href]'));
                                return links.map(link => {
                                    const href = link.getAttribute('href');
                                    if (href) {
                                        if (href.startsWith('/')) {
                                            return window.location.origin + href;
                                        } else if (href.startsWith('http')) {
                                            return href;
                                        }
                                    }
                                    return null;
                                }).filter(Boolean);
                            }
                        ''')
                        
                        new_chart_urls = 0
                        for link in links:
                            if self.is_chart_url(link):
                                if self.categorize_url(link):
                                    new_chart_urls += 1
                            elif self.is_potential_chart_page(link) and link not in self.visited_pages:
                                urls_to_visit.append(link)
                        
                        if new_chart_urls > 0:
                            print(f"  Found {new_chart_urls} new chart URLs")
                        
                        await asyncio.sleep(0.5)
                        
                    except Exception as e:
                        print(f"  Error: {e}")
                        continue
                
            finally:
                await browser.close()
    
    def is_chart_url(self, url):
        """Check if URL is a chart URL"""
        if not url or not isinstance(url, str):
            return False
        return bool(re.search(r'/charts/\d+/(week|rock)\d+\.html', url))
    
    def is_potential_chart_page(self, url):
        """Check if URL might contain chart links"""
        if not url or not isinstance(url, str):
            return False
        
        # Only check tunecaster.com URLs
        if 'tunecaster.com' not in url:
            return False
        
        indicators = [
            '/chart', '/rock', '/pop', '/charts',
            'chart0.html', 'chart1.html', 'chart2.html', 'chart3.html',
            'chart4.html', 'chart5.html', 'chart6.html', 'chart7.html',
            'chart8.html', 'chart9.html', 'rock0.html', 'rock1.html',
            'rock2.html', 'rock3.html', 'rock4.html', 'rock5.html',
            'rock6.html', 'rock7.html', 'rock8.html', 'rock9.html'
        ]
        
        return any(indicator in url.lower() for indicator in indicators)
    
    def categorize_url(self, url):
        """Categorize URL as pop or rock and add to appropriate set"""
        if re.search(r'/charts/\d+/week\d+\.html', url):
            if url not in self.pop_urls:
                self.pop_urls.add(url)
                return True
        elif re.search(r'/charts/\d+/rock\d+\.html', url):
            if url not in self.rock_urls:
                self.rock_urls.add(url)
                return True
        return False
    
    def show_results(self):
        """Show collection results"""
        pop_count = len(self.pop_urls)
        rock_count = len(self.rock_urls)
        total_count = pop_count + rock_count
        
        print("\n" + "="*60)
        print("CHART URLS COLLECTION RESULTS")
        print("="*60)
        print(f"POP CHART URLs: {pop_count}")
        print(f"ROCK CHART URLs: {rock_count}")
        print(f"TOTAL CHART URLs: {total_count}")
        
        # Show decade breakdown
        if self.pop_urls:
            print(f"\nPop URLs by decade:")
            pop_decades = self.analyze_by_decade(list(self.pop_urls))
            for decade, count in sorted(pop_decades.items()):
                print(f"  {self.get_decade_name(decade)}: {count}")
        
        if self.rock_urls:
            print(f"\nRock URLs by decade:")
            rock_decades = self.analyze_by_decade(list(self.rock_urls))
            for decade, count in sorted(rock_decades.items()):
                print(f"  {self.get_decade_name(decade)}: {count}")
        
        # Show samples
        if self.pop_urls:
            print(f"\nSample Pop URLs:")
            for i, url in enumerate(sorted(list(self.pop_urls))[:3], 1):
                print(f"  {i}. {url}")
        
        if self.rock_urls:
            print(f"\nSample Rock URLs:")
            for i, url in enumerate(sorted(list(self.rock_urls))[:3], 1):
                print(f"  {i}. {url}")
        
        print("="*60)
    
    def analyze_by_decade(self, urls):
        """Analyze URLs by decade"""
        decade_count = {}
        for url in urls:
            match = re.search(r'/charts/(\d{2})/', url)
            if match:
                decade = match.group(1)
                decade_count[decade] = decade_count.get(decade, 0) + 1
        return decade_count
    
    def get_decade_name(self, decade_code):
        """Get decade name from code"""
        decade_map = {
            '00': '2000s', '10': '2010s', '20': '2020s',
            '60': '1960s', '70': '1970s', '80': '1980s', '90': '1990s'
        }
        return decade_map.get(decade_code, f"Decade {decade_code}")
    
    def save_to_json(self):
        """Save results to JSON file"""
        try:
            data = {
                'collection_info': {
                    'total_urls': len(self.pop_urls) + len(self.rock_urls),
                    'pop_urls_count': len(self.pop_urls),
                    'rock_urls_count': len(self.rock_urls),
                    'collection_date': datetime.now().isoformat(),
                    'pages_visited': len(self.visited_pages)
                },
                'pop_urls': sorted(list(self.pop_urls)),
                'rock_urls': sorted(list(self.rock_urls))
            }
            
            with open('urls.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"\n✓ Results saved to: urls.json")
            print(f"✓ Pop URLs: {len(self.pop_urls)}")
            print(f"✓ Rock URLs: {len(self.rock_urls)}")
            print(f"✓ Total URLs: {len(self.pop_urls) + len(self.rock_urls)}")
            
        except Exception as e:
            print(f"✗ Error saving results: {e}")

async def main():
    finder = ChartURLFinder()
    
    print("TuneCaster Chart URL Finder")
    print("Discovering all chart URLs from website")
    print("="*60)
    
    try:
        await finder.find_all_chart_urls()
        print("\n✓ URL discovery completed!")
        
    except KeyboardInterrupt:
        print("\n✗ Discovery interrupted")
    except Exception as e:
        print(f"\n✗ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())