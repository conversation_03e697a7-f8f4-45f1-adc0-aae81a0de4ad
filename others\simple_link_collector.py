import asyncio
import json
import re
import os
from playwright.async_api import async_playwright
from urllib.parse import urljoin
from datetime import datetime

class SimpleTuneCasterLinkCollector:
    def __init__(self, start_year=1964, end_year=1979):
        self.base_url = "https://tunecaster.com"
        self.start_year = start_year
        self.end_year = end_year
        self.collected_links = []
        
    async def collect_links(self):
        """Collect all chart links from specified year range"""
        print(f"Collecting links from {self.start_year} to {self.end_year}...")
        
        # All decade pages that might contain our target years
        decade_pages = [
            'https://tunecaster.com/chart6.html',  # 1960s
            'https://tunecaster.com/chart7.html',  # 1970s
            'https://tunecaster.com/chart8.html',  # 1980s (just in case)
        ]
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            try:
                for decade_url in decade_pages:
                    print(f"Scraping: {decade_url}")
                    links = await self.get_all_links_from_page(page, decade_url)
                    filtered_links = self.filter_links_by_year(links)
                    self.collected_links.extend(filtered_links)
                    print(f"Found {len(filtered_links)} links from {decade_url}")
                    await asyncio.sleep(1)
                    
            finally:
                await browser.close()
        
        # Remove duplicates and sort
        self.collected_links = list(set(self.collected_links))
        self.collected_links.sort(key=self.get_sort_key)
        
        return self.collected_links
    
    async def get_all_links_from_page(self, page, url):
        """Extract all links from a page"""
        try:
            await page.goto(url, timeout=30000)
            await page.wait_for_timeout(2000)
            
            # Get all href attributes
            links = await page.evaluate('''
                () => {
                    const anchors = document.querySelectorAll('a[href]');
                    return Array.from(anchors).map(a => a.href);
                }
            ''')
            
            # Convert relative URLs to absolute
            absolute_links = []
            for link in links:
                if link.startswith('/'):
                    link = urljoin(self.base_url, link)
                absolute_links.append(link)
            
            return absolute_links
            
        except Exception as e:
            print(f"Error scraping {url}: {e}")
            return []
    
    def filter_links_by_year(self, links):
        """Filter links to only include those from target year range"""
        filtered = []
        
        for link in links:
            if self.is_chart_link(link):
                year = self.extract_year_from_link(link)
                if year and self.start_year <= year <= self.end_year:
                    filtered.append(link)
        
        return filtered
    
    def is_chart_link(self, url):
        """Check if URL is a chart link"""
        patterns = [
            r'/charts/\d+/week\d+\.html',  # Pop charts
            r'/charts/\d+/rock\d+\.html',  # Rock charts
        ]
        
        return any(re.search(pattern, url) for pattern in patterns)
    
    def extract_year_from_link(self, url):
        """Extract 4-digit year from chart URL"""
        match = re.search(r'/charts/(\d{2})/(?:week|rock)(\d{4})\.html', url)
        if not match:
            return None
        
        decade = int(match.group(1))
        week_code = match.group(2)
        year_suffix = int(week_code[:2])
        
        # Convert 2-digit year to 4-digit year based on decade
        if decade >= 60:  # 1960s, 1970s, etc.
            return 1900 + year_suffix
        else:  # 2000s, 2010s, etc.
            return 2000 + year_suffix
    
    def get_sort_key(self, url):
        """Generate sort key for chronological ordering"""
        match = re.search(r'/charts/(\d{2})/(?:week|rock)(\d{4})\.html', url)
        if not match:
            return (9999, 99, 'z')
        
        decade = int(match.group(1))
        week_code = match.group(2)
        year_suffix = int(week_code[:2])
        week = int(week_code[2:])
        
        # Full year
        if decade >= 60:
            full_year = 1900 + year_suffix
        else:
            full_year = 2000 + year_suffix
        
        # Chart type
        chart_type = 'rock' if 'rock' in url else 'pop'
        
        return (full_year, week, chart_type)
    
    def save_to_file(self, filename=None):
        """Save collected links to JSON file"""
        if not filename:
            filename = f'data/links_{self.start_year}_{self.end_year}.json'
        
        os.makedirs('data', exist_ok=True)
        
        # Organize data
        data = {
            'collection_info': {
                'start_year': self.start_year,
                'end_year': self.end_year,
                'total_links': len(self.collected_links),
                'collected_at': datetime.now().isoformat()
            },
            'links': self.collected_links,
            'links_by_year': self.group_links_by_year()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Links saved to: {filename}")
        return filename
    
    def group_links_by_year(self):
        """Group links by year and chart type"""
        grouped = {}
        
        for link in self.collected_links:
            year = self.extract_year_from_link(link)
            if not year:
                continue
            
            chart_type = 'rock' if 'rock' in link else 'pop'
            year_str = str(year)
            
            if year_str not in grouped:
                grouped[year_str] = {'pop': [], 'rock': []}
            
            grouped[year_str][chart_type].append(link)
        
        return grouped
    
    def print_summary(self):
        """Print collection summary"""
        print(f"\nCollection Summary ({self.start_year}-{self.end_year})")
        print("=" * 50)
        
        year_stats = {}
        pop_total = rock_total = 0
        
        for link in self.collected_links:
            year = self.extract_year_from_link(link)
            chart_type = 'rock' if 'rock' in link else 'pop'
            
            if year:
                if year not in year_stats:
                    year_stats[year] = {'pop': 0, 'rock': 0}
                year_stats[year][chart_type] += 1
                
                if chart_type == 'pop':
                    pop_total += 1
                else:
                    rock_total += 1
        
        # Print year breakdown
        for year in sorted(year_stats.keys()):
            pop = year_stats[year]['pop']
            rock = year_stats[year]['rock']
            total = pop + rock
            print(f"{year}: {total:3d} links (Pop: {pop:2d}, Rock: {rock:2d})")
        
        print("-" * 50)
        print(f"Total Links: {len(self.collected_links)}")
        print(f"Pop Charts: {pop_total}")
        print(f"Rock Charts: {rock_total}")
        print(f"Years: {self.start_year}-{self.end_year}")

async def main():
    # You can change these years as needed
    START_YEAR = 1964
    END_YEAR = 1979
    
    collector = SimpleTuneCasterLinkCollector(START_YEAR, END_YEAR)
    
    print(f"TuneCaster Link Collector ({START_YEAR}-{END_YEAR})")
    print("=" * 50)
    
    try:
        # Collect links
        links = await collector.collect_links()
        
        # Print summary
        collector.print_summary()
        
        # Save to file
        filename = collector.save_to_file()
        
        print(f"\nCollection completed!")
        print(f"Found {len(links)} links from {START_YEAR} to {END_YEAR}")
        
        # Show first few examples
        if links:
            print(f"\nFirst 5 links:")
            for i, link in enumerate(links[:5], 1):
                year = collector.extract_year_from_link(link)
                chart_type = 'Rock' if 'rock' in link else 'Pop'
                print(f"{i}. {year} {chart_type}: {link}")
        
    except KeyboardInterrupt:
        print("\nCollection interrupted")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())