import csv
import json
import re

def convert_artist_format(input_file, output_file):
    """
    Convert artist names from list format to the specified format.
    Input: ['Artist Name'] or ["Artist Name"] 
    Output: {"Artist","Name","etc"}
    """
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        
        reader = csv.DictReader(infile)
        fieldnames = reader.fieldnames
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for row in reader:
            # Get the artist column
            artist_data = row['artist']
            
            try:
                # Parse the artist list (handle both single and double quotes)
                # Remove outer brackets and quotes, then split by comma if multiple artists
                artist_data_clean = artist_data.strip()
                
                # Handle list format like ['Artist'] or ["Artist"] or ['Artist1', 'Artist2']
                if artist_data_clean.startswith('[') and artist_data_clean.endswith(']'):
                    # Remove outer brackets
                    inner_content = artist_data_clean[1:-1]
                    
                    # Parse the list content - handle both single and double quotes
                    artists = []
                    
                    # Use regex to find quoted strings
                    pattern = r'["\']([^"\']*)["\']'
                    matches = re.findall(pattern, inner_content)
                    
                    if matches:
                        artists = matches
                    else:
                        # Fallback: try to parse as JSON
                        try:
                            artists = json.loads(artist_data_clean)
                        except:
                            # If all else fails, use the original data
                            artists = [artist_data_clean]
                    
                    # Convert each artist name to the desired format
                    converted_artists = []
                    for artist in artists:
                        # Split artist name by spaces and create the format {"word1","word2","word3"}
                        words = artist.strip().split()
                        if words:
                            formatted_artist = '{"' + '","'.join(words) + '"}'
                            converted_artists.append(formatted_artist)
                    
                    # Join multiple artists if there are any
                    if converted_artists:
                        row['artist'] = ' '.join(converted_artists)
                    else:
                        row['artist'] = artist_data  # Keep original if conversion fails
                        
                else:
                    # If not in list format, treat as plain text
                    words = artist_data.strip().split()
                    if words:
                        row['artist'] = '{"' + '","'.join(words) + '"}'
                    
            except Exception as e:
                print(f"Error processing row: {artist_data}, Error: {e}")
                # Keep original data if conversion fails
                pass
            
            writer.writerow(row)

if __name__ == "__main__":
    input_file = "data/charts_data.csv"
    output_file = "data/charts_data_converted.csv"
    
    print("Converting artist format...")
    convert_artist_format(input_file, output_file)
    print(f"Conversion complete! Output saved to: {output_file}")
    
    # Show a few examples of the conversion
    print("\nFirst 5 rows of converted data:")
    with open(output_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader):
            if i < 5:
                print(f"Artist: {row['artist']}")
            else:
                break
