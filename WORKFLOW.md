```mermaid
flowchart TD
    subgraph Init[Initialization]
        A([Start]) --> B[Load Configuration]
        B --> C[Initialize Scraper]
        C --> D[Load Previous Progress]
    end

    subgraph Collect[URL Collection]
        E[Discover Chart URLs] --> F[Rock Charts]
        E --> G[Pop Charts]
        F & G --> H[URL Lists]
    end

    subgraph Process[Data Processing]
        I[Process Each URL] --> J[Fetch Page]
        J --> K[Extract Data]
        K --> L[Parse Artists/Titles]
        L --> M[Create JSON Records]
    end

    subgraph Save[Data Storage]
        N[Save Progress] --> O[Update Progress File]
        O --> P[Save to charts_data.json]
        P --> Q[Generate Summary]
    end

    Init --> Collect
    Collect --> Process
    Process --> Save
    Save --> R([End])

    style Init fill:#e1f5fe,stroke:#01579b
    style Collect fill:#fff3e0,stroke:#e65100
    style Process fill:#f3e5f5,stroke:#4a148c
    style Save fill:#e8f5e9,stroke:#1b5e20
    style A fill:#b3e5fc,stroke:#01579b
    style R fill:#b3e5fc,stroke:#01579b
```