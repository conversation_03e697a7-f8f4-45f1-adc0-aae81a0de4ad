#!/usr/bin/env python3
"""
Script to remove all URLs from 1964-1979 from scraper_progress.json
1964 theke 1979 projonto ja ja url ache sob delete kore dibe
"""

import json
import re
import os
from datetime import datetime

def extract_year_from_url(url):
    """Extract 4-digit year from chart URL"""
    # Pattern: /charts/DD/weekYYWW.html or /charts/DD/rockYYWW.html
    # where DD = decade, YY = year, WW = week
    match = re.search(r'/charts/(\d{2})/(?:week|rock)(\d{4})\.html', url)
    if not match:
        return None
    
    decade = int(match.group(1))
    year_week = match.group(2)
    year_suffix = int(year_week[:2])  # First 2 digits
    
    # Convert to full year based on decade
    if decade == 60:  # 1960s
        full_year = 1900 + year_suffix
    elif decade == 70:  # 1970s  
        full_year = 1900 + year_suffix
    elif decade == 80:  # 1980s
        full_year = 1900 + year_suffix
    else:
        # Default logic for other decades
        if year_suffix >= 60:
            full_year = 1900 + year_suffix
        else:
            full_year = 2000 + year_suffix
    
    return full_year

def filter_urls_by_year_range(urls, start_year, end_year):
    """Filter out URLs that fall within the specified year range"""
    filtered_urls = []
    removed_urls = []
    
    for url in urls:
        year = extract_year_from_url(url)
        if year and start_year <= year <= end_year:
            # This URL is in the range to be removed
            removed_urls.append(url)
        else:
            # Keep this URL
            filtered_urls.append(url)
    
    return filtered_urls, removed_urls

def main():
    progress_file = 'data/scraper_progress.json'
    backup_file = 'data/scraper_progress_backup.json'
    
    print("1964-1979 URL Remover")
    print("=" * 50)
    print("এই স্ক্রিপ্ট 1964 থেকে 1979 পর্যন্ত সব URL মুছে দেবে")
    print()
    
    # Check if file exists
    if not os.path.exists(progress_file):
        print(f"Error: {progress_file} file not found!")
        return
    
    # Read current progress file
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
    except Exception as e:
        print(f"Error reading {progress_file}: {e}")
        return
    
    # Get current URLs
    current_urls = progress_data.get('processed_urls', [])
    print(f"Current total URLs: {len(current_urls)}")
    
    # Filter URLs to remove 1964-1979
    filtered_urls, removed_urls = filter_urls_by_year_range(current_urls, 1964, 1979)
    
    print(f"URLs to be removed (1964-1979): {len(removed_urls)}")
    print(f"URLs to keep: {len(filtered_urls)}")
    
    if len(removed_urls) == 0:
        print("No URLs found in the 1964-1979 range. Nothing to remove.")
        return
    
    # Show some examples of URLs being removed
    print(f"\nExamples of URLs being removed:")
    for i, url in enumerate(removed_urls[:10], 1):
        year = extract_year_from_url(url)
        chart_type = 'Rock' if 'rock' in url else 'Pop'
        print(f"  {i:2d}. {year} {chart_type}: {url}")
    
    if len(removed_urls) > 10:
        print(f"  ... and {len(removed_urls) - 10} more URLs")
    
    # Ask for confirmation
    print(f"\nConfirmation:")
    print(f"- Remove {len(removed_urls)} URLs from 1964-1979")
    print(f"- Keep {len(filtered_urls)} URLs from other years")
    print(f"- Create backup at {backup_file}")
    
    response = input("\nProceed? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    # Create backup
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
        print(f"Backup created: {backup_file}")
    except Exception as e:
        print(f"Error creating backup: {e}")
        return
    
    # Update progress data
    progress_data['processed_urls'] = filtered_urls
    progress_data['last_modified'] = datetime.now().isoformat()
    progress_data['modification_note'] = f"Removed {len(removed_urls)} URLs from 1964-1979 range"
    
    # Save updated file
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
        print(f"Updated file saved: {progress_file}")
    except Exception as e:
        print(f"Error saving updated file: {e}")
        return
    
    # Print summary
    print(f"\nOperation completed successfully!")
    print(f"- Removed: {len(removed_urls)} URLs (1964-1979)")
    print(f"- Remaining: {len(filtered_urls)} URLs")
    print(f"- Backup: {backup_file}")
    print(f"- Updated: {progress_file}")
    
    # Show year breakdown of removed URLs
    year_counts = {}
    for url in removed_urls:
        year = extract_year_from_url(url)
        if year:
            if year not in year_counts:
                year_counts[year] = {'pop': 0, 'rock': 0}
            chart_type = 'rock' if 'rock' in url else 'pop'
            year_counts[year][chart_type] += 1
    
    print(f"\nRemoved URLs by year:")
    for year in sorted(year_counts.keys()):
        pop = year_counts[year]['pop']
        rock = year_counts[year]['rock']
        total = pop + rock
        print(f"  {year}: {total:3d} URLs (Pop: {pop:2d}, Rock: {rock:2d})")

if __name__ == "__main__":
    main()